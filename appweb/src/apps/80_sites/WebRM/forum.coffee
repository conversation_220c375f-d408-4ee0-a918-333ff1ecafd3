UserModel = MODEL 'User'

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({req, user}) ->
  # webComment 包含了 forumAdmin里的权限，但是以防之后有某一个进行了变更，所以都加上了
  if (req.isAllowed 'webComment',user) or (req.isAllowed 'forumAdmin',user)
    return {name: 'Forum', url: '/1.5/forum', index: 1,isIfrm: 1}
  return null
MenuRegistryModel.registerMenu('forum', getMenu)

APP 'forum'

GET (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    isZh = req.locale() in ['zh' , 'zh-cn']
    # resp.ckup 'wecard-list'
    if (UserModel.accessAllowed 'forumAdmin', user) or (UserModel.accessAllowed 'webComment', user)
      encodeUrl = encodeURIComponent('/1.5/forum')
      resp.redirect "/adminindex?d=#{encodeUrl}"
      # resp.ckup 'forum', {isZh:isZh}, '_', {noref:true, side:1}
    else
      resp.ckup 'contact-us-vip', {}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}

# VIEW 'forum', ->
#   iframe src:'/1.5/forum',
#   width:'700px',
#   height:'700px',
#   onload:'this.height=document.body.offsetHeight;'
